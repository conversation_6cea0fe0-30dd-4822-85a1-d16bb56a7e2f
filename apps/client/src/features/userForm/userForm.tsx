import { UserIcon } from "lucide-react";
import { useTranslation } from "react-i18next";
import { z } from "zod/v4";
import { useUserPermissions } from "@/api/auth";
import type { User } from "@/api/users";
import { Form } from "@/components/form";
import { Button } from "@/components/ui/button";
import { Card, CardAction, CardContent, CardHeader } from "@/components/ui/card";
import { canUpdateUsers } from "@/utils/permissions";
import Email from "./fields/email";
import FirstName from "./fields/firstName";
import LastName from "./fields/lastName";
import Role from "./fields/role";
import type { UserForm } from "./types";

const schema: z.ZodSchema<UserForm> = z.object({
    email: z.email(),
    firstName: z.string().min(1),
    lastName: z.string().min(1),
    role_id: z.string().min(1),
});

type Props = {
    editMode?: "classic" | "inline";
    user?: User;
    onSubmit: (data: UserForm) => void;
};

export default function UserForm({ editMode = "classic", user, onSubmit }: Props) {
    const { t } = useTranslation(["users", "common"]);
    const { data: permissions } = useUserPermissions();
    const isNew = editMode === "classic";
    const canUpdate = canUpdateUsers(permissions);
    const readonly = !canUpdate;

    return (
        <Card className="col-span-2">
            <CardHeader>
                <div className="flex items-center gap-2">
                    <UserIcon className="size-5" />
                    {t("primaryDataSection")}
                </div>
            </CardHeader>
            <Form
                schema={schema}
                onSubmit={onSubmit}
                defaultValues={{
                    email: user?.email || "",
                    firstName: user?.first_name || "",
                    lastName: user?.last_name || "",
                    role_id: user?.role_id || "",
                }}
                editMode={editMode}
            >
                <CardContent>
                    <div className="grid gap-4 md:grid-cols-2">
                        <FirstName readonly={readonly} />
                        <LastName readonly={readonly} />
                    </div>
                    <div className="mt-4 flex flex-col gap-4">
                        <Email readonly={!isNew || readonly} />
                        <Role disabled={readonly} />
                    </div>

                    <CardAction>{isNew && <Button type="submit">{t("common:save")}</Button>}</CardAction>
                </CardContent>
            </Form>
        </Card>
    );
}
